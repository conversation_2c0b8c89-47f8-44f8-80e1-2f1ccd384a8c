﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.Concrete
{
    public class CompanyAdress : ICompanyEntity
    {
        [Key]
        public int CompanyAdressID { get; set; }
        public int CompanyID { get; set; }
        public int CityID { get; set; }
        public int TownID { get; set; }
        public string Adress { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreationDate { get; set; }
        public DateTime? DeletedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
    }
}
