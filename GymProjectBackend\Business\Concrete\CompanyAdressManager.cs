﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class CompanyAdressManager : ICompanyAdressService
    {
        ICompanyAdressDal _companyAdressDal;
        private readonly ICompanyContext _companyContext;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public CompanyAdressManager(ICompanyAdressDal companyAdress, ICompanyContext companyContext, IHttpContextAccessor httpContextAccessor)
        {
            _companyAdressDal = companyAdress;
            _companyContext = companyContext;
            _httpContextAccessor = httpContextAccessor;
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("CompanyAdress")]
        [ValidationAspect(typeof(CompanyAdressValidator))]
        public IResult Add(CompanyAdress companyAdress)
        {
            _companyAdressDal.Add(companyAdress);
            return new SuccessResult(Messages.CompanyAdressAdded);
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("CompanyAdress")]
        public IResult Delete(int id)
        {
            _companyAdressDal.Delete(id);
            return new SuccessResult(Messages.CompanyAdressDeleted);
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 240, "CompanyAdress", "Configuration")]
        public IDataResult<List<CompanyAdress>> GetAll()
        {
            // Role-based filtering
            if (IsOwner())
            {
                // Owner tüm şirketlerin adreslerini görebilir
                return new SuccessDataResult<List<CompanyAdress>>(_companyAdressDal.GetAll());
            }
            else
            {
                // Admin/User sadece kendi şirketinin adresini görebilir
                int companyId = _companyContext.GetCompanyId();
                var companyAddresses = _companyAdressDal.GetAll(ca => ca.CompanyID == companyId);
                return new SuccessDataResult<List<CompanyAdress>>(companyAddresses);
            }
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("CompanyAdress")]
        [ValidationAspect(typeof(CompanyAdressValidator))]
        public IResult Update(CompanyAdress companyAdress)
        {
            _companyAdressDal.Update(companyAdress);
            return new SuccessResult(Messages.CompanyAdressUpdated);
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 240, "CompanyAdress", "Details")]
        public IDataResult<List<CompanyAdressDetailDto>> GetCompanyAdressDetails()
        {
            // Role-based filtering
            if (IsOwner())
            {
                // Owner tüm şirketlerin adres detaylarını görebilir
                return new SuccessDataResult<List<CompanyAdressDetailDto>>(_companyAdressDal.GetCompanyAdressDetails());
            }
            else
            {
                // Admin/User sadece kendi şirketinin adres detaylarını görebilir
                var allDetails = _companyAdressDal.GetCompanyAdressDetails();
                int companyId = _companyContext.GetCompanyId();
                var filteredDetails = allDetails.Where(ca => ca.CompanyAdressID == companyId).ToList();
                return new SuccessDataResult<List<CompanyAdressDetailDto>>(filteredDetails);
            }
        }

        /// <summary>
        /// Kullanıcının Owner rolüne sahip olup olmadığını kontrol eder
        /// </summary>
        private bool IsOwner()
        {
            var roleClaims = _httpContextAccessor.HttpContext?.User?.Claims
                ?.Where(c => c.Type == "http://schemas.microsoft.com/ws/2008/06/identity/claims/role")
                ?.Select(c => c.Value);

            return roleClaims?.Contains("owner") == true;
        }

    }
}
